'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { useCart } from '@/contexts/CartContext';
import { cartApi } from '@/lib/api';

export default function TestGSTPage() {
  const { cart, cartSummary, refreshCart } = useCart();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const testGSTAPI = async () => {
    setIsLoading(true);
    try {
      // Test cart API
      const cartData = await cartApi.getCart();
      const summaryData = await cartApi.getCartSummary();
      
      setTestResults({
        cart: cartData,
        summary: summaryData,
        timestamp: new Date().toISOString()
      });
      
      console.log('GST API Test Results:', {
        cart: cartData,
        summary: summaryData
      });
    } catch (error) {
      console.error('GST API Test Failed:', error);
      setTestResults({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    testGSTAPI();
  }, []);

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">GST Breakdown Test</h1>
          <p className="text-gray-600">Testing the new GST breakdown API integration</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Current Cart Context */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Cart Context Data</h2>
            
            {cart && (
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-700 mb-2">Basic Info</h3>
                  <div className="text-sm space-y-1">
                    <p>Items: {cart.items_count}</p>
                    <p>Subtotal: ₹{cart.sub_total}</p>
                    <p>Total: ₹{cart.total_amount}</p>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-700 mb-2">GST Breakdown</h3>
                  <div className="text-sm space-y-1">
                    <p>CGST: ₹{cart.cgst_amount || '0.00'}</p>
                    <p>SGST: ₹{cart.sgst_amount || '0.00'}</p>
                    <p>IGST: ₹{cart.igst_amount || '0.00'}</p>
                    <p>Service Charge: ₹{cart.service_charge || '0.00'}</p>
                  </div>
                </div>

                {cart.tax_breakdown && cart.tax_breakdown.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">Tax Breakdown Array</h3>
                    <div className="space-y-1">
                      {cart.tax_breakdown.map((tax, index) => (
                        <div key={index} className="text-sm flex justify-between">
                          <span>{tax.type} ({tax.rate})</span>
                          <span>₹{tax.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {!cart && (
              <p className="text-gray-500">No cart data available</p>
            )}
          </div>

          {/* API Test Results */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">API Test Results</h2>
              <button
                onClick={testGSTAPI}
                disabled={isLoading}
                className="btn-outline text-sm"
              >
                {isLoading ? 'Testing...' : 'Retest API'}
              </button>
            </div>

            {testResults && (
              <div className="space-y-4">
                {testResults.error ? (
                  <div className="text-red-600 text-sm">
                    <p className="font-medium">Error:</p>
                    <p>{testResults.error}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">Cart API Response</h3>
                      <div className="bg-gray-50 p-3 rounded text-xs">
                        <pre>{JSON.stringify(testResults.cart, null, 2)}</pre>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">Summary API Response</h3>
                      <div className="bg-gray-50 p-3 rounded text-xs">
                        <pre>{JSON.stringify(testResults.summary, null, 2)}</pre>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 flex space-x-4">
          <button
            onClick={refreshCart}
            className="btn-primary"
          >
            Refresh Cart Context
          </button>
          
          <button
            onClick={() => window.location.href = '/cart'}
            className="btn-outline"
          >
            Go to Cart Page
          </button>
        </div>
      </div>
    </MainLayout>
  );
}
