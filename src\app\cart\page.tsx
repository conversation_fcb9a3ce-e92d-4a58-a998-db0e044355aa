'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Minus, Plus, Trash2, ShoppingCart, ArrowRight, Tag } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingButton, LoadingPage } from '@/components/ui/LoadingSpinner';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';

export default function CartPage() {
  const [couponCode, setCouponCode] = useState('');
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);
  
  const { 
    cart, 
    cartSummary, 
    isLoading, 
    updateCartItem, 
    removeCartItem, 
    applyCoupon, 
    removeCoupon 
  } = useCart();
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();
  const router = useRouter();

  const handleQuantityChange = async (itemId: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    try {
      await updateCartItem(itemId, newQuantity);
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to update quantity', message: error.message });
    }
  };

  const handleRemoveItem = async (itemId: number) => {
    try {
      await removeCartItem(itemId);
      showToast({ type: 'success', title: 'Item removed from cart' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to remove item', message: error.message });
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      showToast({ type: 'error', title: 'Please enter a coupon code' });
      return;
    }

    setIsApplyingCoupon(true);
    try {
      await applyCoupon(couponCode.trim());
      setCouponCode('');
      showToast({ type: 'success', title: 'Coupon applied successfully' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to apply coupon', message: error.message });
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  const handleRemoveCoupon = async () => {
    try {
      await removeCoupon();
      showToast({ type: 'success', title: 'Coupon removed' });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to remove coupon', message: error.message });
    }
  };

  const handleCheckout = () => {
    if (!isAuthenticated) {
      router.push(`/auth/login?redirect=${encodeURIComponent('/checkout/address')}`);
      return;
    }
    router.push('/checkout/address');
  };

  if (isLoading) {
    return (
      <MainLayout>
        <LoadingPage message="Loading cart..." />
      </MainLayout>
    );
  }

  if (!isAuthenticated) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <ShoppingCart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Please login to view your cart</h2>
            <p className="text-gray-600 mb-8">You need to be logged in to access your shopping cart</p>
            <Link href="/auth/login" className="btn-primary">
              Login
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!cart || cart.items.length === 0) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <ShoppingCart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-8">Add some services to get started</p>
            <Link href="/" className="btn-primary">
              Browse Services
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Shopping Cart</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cart.items.map((item) => (
              <div key={item.id} className="card p-6">
                <div className="flex items-center space-x-4">
                  {/* Service Image */}
                  <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                    {item.service_image ? (
                      <Image
                        src={item.service_image}
                        alt={item.service_title}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-primary-100 flex items-center justify-center">
                        <span className="text-primary-600 font-semibold text-sm">
                          {item.service_title.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Service Details */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {item.service_title}
                    </h3>
                    <p className="text-sm text-gray-600">{item.service_category}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-lg font-bold text-gray-900">
                        ₹{item.current_service_price}
                      </span>
                      {item.savings !== '0.00' && (
                        <span className="text-sm text-green-600">
                          Save ₹{item.savings}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                      className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                      disabled={item.quantity <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                      className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Total Price */}
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      ₹{item.total_price}
                    </div>
                    <button
                      onClick={() => handleRemoveItem(item.id)}
                      className="text-red-600 hover:text-red-700 mt-2"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            {/* Coupon Section */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Apply Coupon</h3>
              {cartSummary?.coupon_code_applied ? (
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-green-800 font-medium">
                      {cartSummary.coupon_code_applied}
                    </span>
                  </div>
                  <button
                    onClick={handleRemoveCoupon}
                    className="text-red-600 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </div>
              ) : (
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value)}
                    placeholder="Enter coupon code"
                    className="flex-1 input"
                  />
                  <LoadingButton
                    onClick={handleApplyCoupon}
                    isLoading={isApplyingCoupon}
                    className="btn-outline"
                  >
                    Apply
                  </LoadingButton>
                </div>
              )}
            </div>

            {/* Order Summary */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">₹{cartSummary?.sub_total}</span>
                </div>
                {cartSummary?.discount_amount !== '0.00' && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount</span>
                    <span>-₹{cartSummary?.discount_amount}</span>
                  </div>
                )}

                {/* GST Breakdown */}
                {cartSummary?.tax_breakdown && cartSummary.tax_breakdown.length > 0 && (
                  <div className="border-t pt-3">
                    <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
                      <span>Tax Breakdown</span>
                      <span>₹{cartSummary?.tax_amount}</span>
                    </div>
                    <div className="space-y-1 ml-4">
                      {cartSummary.tax_breakdown.map((tax, index) => (
                        <div key={index} className="flex justify-between text-sm text-gray-600">
                          <span>{tax.type} ({tax.rate})</span>
                          <span>₹{tax.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Fallback for simple tax display */}
                {(!cartSummary?.tax_breakdown || cartSummary.tax_breakdown.length === 0) && cartSummary?.tax_amount !== '0.00' && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tax (GST)</span>
                    <span className="font-medium">₹{cartSummary?.tax_amount}</span>
                  </div>
                )}

                {cartSummary?.minimum_order_fee_applied !== '0.00' && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Service Fee</span>
                    <span className="font-medium">₹{cartSummary?.minimum_order_fee_applied}</span>
                  </div>
                )}
                <div className="border-t pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total</span>
                    <span>₹{cartSummary?.total_amount}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                className="w-full btn-primary mt-6 flex items-center justify-center"
              >
                Proceed to Checkout
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
